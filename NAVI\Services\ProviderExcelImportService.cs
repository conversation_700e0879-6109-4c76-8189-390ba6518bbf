using Newtonsoft.Json.Linq;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Controls;
using NAVI.Services.DAL;

namespace NAVI.Services
{
    /// <summary>
    /// 事业者Excel导入服务
    /// </summary>
    public class ProviderExcelImportService
    {
        private readonly string _databasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "database", "shortstay_app_ver0.9.xlsx");

        /// <summary>
        /// 导入事业者Excel文件
        /// </summary>
        public async Task<ImportResult> ImportProviderExcelAsync(string filePath)
        {
            var result = new ImportResult();

            try
            {
                // 1. 读取源Excel文件数据
                var sourceData = await ReadProviderExcelDataAsync(filePath);

                // 2. 数据预处理和验证
                /*var validationResult = ValidateSourceData(sourceData);
                if (!validationResult.IsValid)
                {
                    result.IsSuccess = false;
                    result.Message = $"数据验证失败: {validationResult.ErrorMessage}";
                    return result;
                }*/

                // 3. 匹配基准数据并汇总整理
                //var consolidatedData = await ConsolidateAndMatchDataAsync(sourceData);

                // 4. 数据去重和合并
                var finalData = await MatchWithBaseDataAsync(sourceData);

                // 5. 导入到受给者数据表
                var importedCount = await ImportToRecipientDataAsync(finalData);

                result.IsSuccess = true;
                result.ImportedRecordCount = importedCount;
                result.Message = $"成功导入 {importedCount} 条记录";
                //result.Details = CreateImportSummary(sourceData, consolidatedData, finalData);
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Message = $"导入失败: {ex.Message}";
                result.ImportedRecordCount = 0;
            }

            return result;
        }

        /// <summary>
        /// 导入OCR识别的数据到Excel
        /// </summary>
        public async Task<ImportResult> ImportOcrDataToExcelAsync(List<RecipientImportData> ocrData)
        {
            var result = new ImportResult();

            try
            {
                // 直接导入OCR数据到受给者数据表
                var importedCount = await ImportToRecipientDataAsync(ocrData);

                result.IsSuccess = true;
                result.ImportedRecordCount = importedCount;
                result.Message = $"OCR数据成功导入 {importedCount} 条记录";
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.Message = $"OCR数据导入失败: {ex.Message}";
                result.ImportedRecordCount = 0;
            }

            return result;
        }

        /// <summary>
        /// 读取事业者Excel文件数据
        /// </summary>
        private async Task<ProviderExcelData> ReadProviderExcelDataAsync(string filePath)
        {
            return await Task.Run(() =>
            {
                var data = new ProviderExcelData();

                using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    IWorkbook workbook;
                    if (filePath.EndsWith(".xlsx"))
                        workbook = new XSSFWorkbook(fileStream);
                    else
                        workbook = new HSSFWorkbook(fileStream);

                    var sheet = workbook.GetSheetAt(0); // 假设数据在第一个工作表

                    // 读取基本信息（根据截图中的具体位置，Excel行列从0开始计算）
                    // 年月信息：令和7年2月 (行8, 列24-26)
                    data.年月 = ReadCellRange(sheet, 7, 21, 7, 31);

                    // 受给者证番号：********** (行10, 列25)
                    data.受给者证番号 = ReadCellValue(sheet, 9, 10);

                    // 支给决定障害者氏名：铃木太郎 (行11, 列25)
                    data.支给决定障害者氏名 = ReadCellValue(sheet, 10, 10);

                    // 支给决定に係る障害児氏名：铃木太郎 (行13, 列25)
                    data.支给决定に係る障害児氏名 = ReadCellValue(sheet, 12, 10);

                    // 事业所番号：********** (行10, 列32)
                    data.事业所番号 = ReadCellValue(sheet, 9, 22);


                    // 事业者及びその事业所の名称：サポートハウス東京 (行11-12, 列32)
                    data.事业者及びその事业所の名称 = ReadCellValue(sheet, 10, 22); ;

                    // 障害支援区分：1級地 (行14, 列32)
                    data.障害支援区分 = ReadCellValue(sheet, 14, 15);

                    data.地域区分 = ReadCellValue(sheet, 13, 25);

                    // 开始年月日：2025-2-1 (行18, 列8)
                    data.开始年月日 = ReadCellValue(sheet, 17, 9);

                    // 终了年月日：2025-2-28 (行18, 列20)
                    data.终了年月日 = ReadCellValue(sheet, 17, 16);

                    // 利用日数：28 (行18, 列32)
                    data.利用日数 = ReadCellValue(sheet, 17, 28);

                    // 读取服务明细
                    data.旧身体療護施設区分 = ReadCellValue(sheet, 14, 29);

                    // 读取服务明细
                    data.精神科医療連携体制加算 = ReadCellValue(sheet, 15, 29);

                    // 读取服务明细
                    data.服务明细 = ReadServiceDetails(sheet);
                }

                return data;
            });
        }

        /// <summary>
        /// 读取单元格范围的值并拼接
        /// </summary>
        private string ReadCellRange(ISheet sheet, int startRow, int startCol, int endRow, int endCol)
        {
            var values = new List<string>();

            for (int row = startRow; row <= endRow; row++)
            {
                for (int col = startCol; col <= endCol; col++)
                {
                    var cellValue = ReadCellValue(sheet, row, col);
                    if (!string.IsNullOrEmpty(cellValue))
                    {
                        values.Add(cellValue);
                    }
                }
            }

            return string.Join("", values);
        }

        /// <summary>
        /// 读取单元格值
        /// </summary>
        private string ReadCellValue(ISheet sheet, int rowIndex, int colIndex)
        {
            try
            {
                var row = sheet.GetRow(rowIndex);
                if (row == null) return string.Empty;

                var cell = row.GetCell(colIndex);
                if (cell == null) return string.Empty;

                switch (cell.CellType)
                {
                    case CellType.String:
                        return cell.StringCellValue ?? string.Empty;
                    case CellType.Numeric:
                        if (DateUtil.IsCellDateFormatted(cell))
                            return cell.DateCellValue.ToString("yyyy/MM/dd");
                        return cell.NumericCellValue.ToString();
                    case CellType.Boolean:
                        return cell.BooleanCellValue.ToString();
                    case CellType.Formula:
                        var evaluator = sheet.Workbook.GetCreationHelper().CreateFormulaEvaluator();
                        var result = evaluator.Evaluate(cell);
                        switch (result.CellType)
                        {
                            case CellType.String:
                                return result.StringValue ?? string.Empty;
                            case CellType.Numeric:
                                return result.NumberValue.ToString();
                            case CellType.Boolean:
                                return result.BooleanValue.ToString();
                            default:
                                return string.Empty;
                        }
                    default:
                        return string.Empty;
                }
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 读取服务明细
        /// </summary>
        private List<ServiceDetail> ReadServiceDetails(ISheet sheet)
        {
            var details = new List<ServiceDetail>();

            // 根据截图读取服务明细行（从第21行开始，Excel行号从0开始计算）
            // 服务明细区域：行21-30，包含服务代码、服务内容、单定价格、利用日数、当月费定额
            for (int row = 20; row <= 29; row++) // Excel行21-30对应索引20-29
            {
                // 服务代码在列B-D (索引1-3)，需要拼接
                var serviceCode = ReadCellRange(sheet, row, 2, row, 8);
                if (string.IsNullOrEmpty(serviceCode)) continue;

                // 服务内容在列I-P (索引8-15)，需要拼接
                var serviceContent = ReadCellRange(sheet, row, 8, row, 15);

                // 单定价格在列Q (索引16)
                var unitPrice = ReadCellValue(sheet, row, 16);

                // 利用日数在列R (索引17)
                var usageDays = ReadCellValue(sheet, row, 21);

                // 当月费定额在列S-T (索引18-19)，需要拼接
                var monthlyAmount = ReadCellRange(sheet, row, 22, row, 28);

                // 当月费定额在列S-T (索引18-19)，需要拼接
                var remark = ReadCellRange(sheet, row, 29, row, 31);

                var detail = new ServiceDetail
                {
                    服务代码 = serviceCode.Trim(),
                    服务内容 = serviceContent.Trim(),
                    单定价格 = unitPrice.Trim(),
                    利用日数 = usageDays.Trim(),
                    当月费定额 = monthlyAmount.Trim(),
                    摘要 = remark.Trim()
                };

                // 只添加有效的服务明细
                if (!string.IsNullOrEmpty(detail.服务代码) && detail.服务代码.StartsWith("2"))
                {
                    details.Add(detail);
                }
            }

            return details;
        }

        /// <summary>
        /// 与基准数据匹配
        /// </summary>
        private async Task<List<RecipientImportData>> MatchWithBaseDataAsync(ProviderExcelData sourceData)
        {
            return await Task.Run(() =>
            {
                var results = new List<RecipientImportData>();
                var excelService = new ExcelDataService(_databasePath);

                try
                {
                    // 获取基准数据 - 从ServiceProviders和ServiceCodeMaster表获取数据
                    var serviceProvidersData = excelService.GetSheetData("ServiceProviders");
                    var serviceCodeMasterData = excelService.GetSheetData("ServiceCodeMaster");

                    // 匹配事业者数据 - 根据事業者番号和事業者名称进行匹配
                    var matchedBusiness = serviceProvidersData.FirstOrDefault(b =>
                    {
                        var hasIdMatch = b.ContainsKey("事業者番号") &&
                                         b["事業者番号"] != null &&
                                         b["事業者番号"].ToString() == sourceData.事业所番号;

                        var hasNameMatch = b.ContainsKey("事業者名称") &&
                                           b["事業者名称"] != null &&
                                           b["事業者名称"].ToString().Contains(sourceData.事业者及びその事业所の名称);

                        return hasIdMatch || hasNameMatch;
                    });

                    // 为每个服务明细创建记录
                    foreach (var service in sourceData.服务明细)
                    {
                        // 匹配服务代码数据 - 根据サービスコード进行匹配
                        var matchedService = serviceCodeMasterData.FirstOrDefault(s =>
                        {
                            var hasCodeMatch = s.ContainsKey("サービスコード") &&
                                               s["サービスコード"] != null &&
                                               s["サービスコード"].ToString() == service.服务代码;

                            var hasNameMatch = s.ContainsKey("サービス内容略称") &&
                                               s["サービス内容略称"] != null &&
                                               s["サービス内容略称"].ToString().Contains(service.服务内容);
                            return hasCodeMatch || hasNameMatch;
                        });

                        var importData = new RecipientImportData
                        {
                            No = 0, // 将在导入时设置
                            登録日 = DateTime.Now.ToString("yyyy/MM/dd"),
                            事業者番号 = sourceData.事业所番号,
                            事業者郵便番号 = GetBusinessProperty(matchedBusiness, "郵便番号"),
                            事業者住所 = GetBusinessProperty(matchedBusiness, "所在地"),
                            事業者名称 = sourceData.事业者及びその事业所の名称,
                            代表者名 = GetBusinessProperty(matchedBusiness, "代表者名"),
                            代表者役職 = GetBusinessProperty(matchedBusiness, "代表者役職"),
                            サービス提供年月 = ExtractYearMonth(sourceData.年月),
                            明細書件数 = 1, // 每个服务明细算作1件
                            請求金額 = ParseDecimal(service.当月费定额),
                            第三者評価 = GetBusinessProperty(matchedBusiness, "第三者評価結果"),
                            受給者番号 = sourceData.受给者证番号,
                            支給決定障害者氏名 = sourceData.支给决定障害者氏名,
                            支給決定に係る障害児氏名 = sourceData.支给决定に係る障害児氏名,
                            障害支援区分 = sourceData.障害支援区分,
                            事業者名称2 = sourceData.事业者及びその事业所の名称, // 备用名称
                            地域区分 = sourceData.地域区分,
                            旧身体療護施設区分 = GetServiceProperty(matchedService, "旧身体療護"),
                            精神科医療連携体制加算 = sourceData.精神科医療連携体制加算,
                            開始年月日 = FormatDate(sourceData.开始年月日),
                            終了年月日 = FormatDate(sourceData.终了年月日),
                            利用日数全体 = ParseInt(sourceData.利用日数),
                            サービスコード = service.服务代码,
                            サービス内容 = GetServiceProperty(matchedService, "サービス内容略称", service.服务内容),
                            算定単価額 = GetServiceDecimalProperty(matchedService, "単位数単価", service.单定价格),
                            利用日数 = ParseInt(service.利用日数),
                            当月算定額 = ParseDecimal(service.当月费定额),
                            摘要 = service.摘要,
                            Status = "処理済"
                        };

                        results.Add(importData);
                    }
                }
                finally
                {
                    excelService.Dispose();
                }

                return results;
            });
        }

        /// <summary>
        /// 导入到受给者数据表
        /// </summary>
        private async Task<int> ImportToRecipientDataAsync(List<RecipientImportData> data)
        {
            return await Task.Run(async () =>
            {
                int importedCount = 0;

                try
                {
                    // 使用数据库服务导入到RecipientsData表
                    var databaseService = new DatabaseService();
                    var recipientRepository = new RecipientRepository(databaseService);

                    foreach (var item in data)
                    {
                        // 转换为RecipientData实体
                        var recipientData = new RecipientData
                        {
                            登録日 = item.登録日,
                            事業者番号 = item.事業者番号,
                            事業者郵便番号 = item.事業者郵便番号,
                            事業者住所 = item.事業者住所,
                            事業者名称 = item.事業者名称,
                            代表者名 = item.代表者名,
                            代表者役職 = item.代表者役職,
                            サービス提供年月 = item.サービス提供年月,
                            明細書件数 = item.明細書件数.ToString(),
                            請求金額 = item.請求金額.ToString(),
                            第三者評価 = item.第三者評価,
                            受給者番号 = item.受給者番号,
                            支給決定障害者氏名 = item.支給決定障害者氏名,
                            支給決定に係る障害児氏名 = item.支給決定に係る障害児氏名,
                            障害支援区分 = item.障害支援区分,
                            事業者名称2 = item.事業者名称2,
                            地域区分 = item.地域区分,
                            旧身体療護施設区分 = item.旧身体療護施設区分,
                            精神科医療連携体制加算 = item.精神科医療連携体制加算,
                            開始年月日 = item.開始年月日,
                            終了年月日 = item.終了年月日,
                            利用日数全体 = item.利用日数全体.ToString(),
                            サービスコード = item.サービスコード,
                            サービス内容 = item.サービス内容,
                            算定単価額 = item.算定単価額.ToString(),
                            利用日数 = item.利用日数.ToString(),
                            当月算定額 = item.当月算定額.ToString(),
                            摘要 = item.摘要,
                            status = item.Status
                        };

                        // 插入到数据库
                        await recipientRepository.InsertAsync(recipientData);
                        importedCount++;
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"导入到RecipientsData表失败: {ex.Message}", ex);
                }

                return importedCount;
            });
        }

        /// <summary>
        /// 生成受给者ID
        /// </summary>
        private string GenerateRecipientId(string recipientNumber = null)
        {
            if (!string.IsNullOrEmpty(recipientNumber))
            {
                return "R" + recipientNumber;
            }
            return "R" + DateTime.Now.ToString("yyyyMMddHHmmss") + new Random().Next(100, 999);
        }

        /// <summary>
        /// 获取事业者属性值
        /// </summary>
        private string GetBusinessProperty(Dictionary<string, object> businessData, string propertyName)
        {
            if (businessData?.ContainsKey(propertyName) == true)
            {
                return businessData[propertyName]?.ToString() ?? "";
            }
            return "";
        }

        /// <summary>
        /// 获取服务属性值
        /// </summary>
        private string GetServiceProperty(Dictionary<string, object> serviceData, string propertyName, string defaultValue = "")
        {
            if (serviceData?.ContainsKey(propertyName) == true)
            {
                return serviceData[propertyName]?.ToString() ?? defaultValue;
            }
            return defaultValue;
        }

        /// <summary>
        /// 获取服务属性的decimal值
        /// </summary>
        private decimal GetServiceDecimalProperty(Dictionary<string, object> serviceData, string propertyName, string defaultValue = "")
        {
            if (serviceData?.ContainsKey(propertyName) == true)
            {
                var value = serviceData[propertyName]?.ToString();
                if (!string.IsNullOrEmpty(value) && decimal.TryParse(value, out decimal result))
                {
                    return result;
                }
            }
            return ParseDecimal(defaultValue);
        }

        /// <summary>
        /// 从年月字符串中提取YYYYMM格式
        /// </summary>
        private string ExtractYearMonth(string yearMonthString)
        {
            if (string.IsNullOrEmpty(yearMonthString)) return DateTime.Now.ToString("yyyyMM");

            // 处理"令和7年2月"格式
            if (yearMonthString.Contains("令和"))
            {
                try
                {
                    var match = System.Text.RegularExpressions.Regex.Match(yearMonthString, @"令和(\d+)年(\d+)月");
                    if (match.Success)
                    {
                        var reiwaYear = int.Parse(match.Groups[1].Value);
                        var month = int.Parse(match.Groups[2].Value);
                        var westernYear = reiwaYear + 2018; // 令和元年是2019年
                        return $"{westernYear:D4}{month:D2}";
                    }
                }
                catch
                {
                    // 解析失败时返回当前年月
                }
            }

            // 处理其他格式或返回当前年月
            return DateTime.Now.ToString("yyyyMM");
        }

        /// <summary>
        /// 格式化日期
        /// </summary>
        private string FormatDate(string dateString)
        {
            if (string.IsNullOrEmpty(dateString)) return "";

            // 尝试解析各种日期格式
            if (DateTime.TryParse(dateString, out DateTime date))
            {
                return date.ToString("yyyy/MM/dd");
            }

            // 处理特殊格式如 "2025-2-1"
            if (dateString.Contains("-"))
            {
                var parts = dateString.Split('-');
                if (parts.Length == 3 &&
                    int.TryParse(parts[0], out int year) &&
                    int.TryParse(parts[1], out int month) &&
                    int.TryParse(parts[2], out int day))
                {
                    return new DateTime(year, month, day).ToString("yyyy/MM/dd");
                }
            }

            return dateString;
        }

        /// <summary>
        /// 计算负担额（简单计算：月额费用的10%）
        /// </summary>
        private string CalculateBurdenAmount(string monthlyAmount)
        {
            if (string.IsNullOrEmpty(monthlyAmount)) return "0";

            // 移除非数字字符
            var cleanAmount = System.Text.RegularExpressions.Regex.Replace(monthlyAmount, @"[^\d.]", "");

            if (decimal.TryParse(cleanAmount, out decimal amount))
            {
                var burden = Math.Round(amount * 0.1m, 0); // 10%负担，四舍五入到整数
                return burden.ToString();
            }

            return "0";
        }

        /// <summary>
        /// 解析十进制数值
        /// </summary>
        private decimal ParseDecimal(string value)
        {
            if (string.IsNullOrEmpty(value)) return 0;

            // 移除非数字字符（保留小数点）
            var cleanValue = System.Text.RegularExpressions.Regex.Replace(value, @"[^\d.]", "");

            if (decimal.TryParse(cleanValue, out decimal result))
            {
                return result;
            }

            return 0;
        }

        /// <summary>
        /// 解析整数值
        /// </summary>
        private int ParseInt(string value)
        {
            if (string.IsNullOrEmpty(value)) return 0;

            // 移除非数字字符
            var cleanValue = System.Text.RegularExpressions.Regex.Replace(value, @"[^\d]", "");

            if (int.TryParse(cleanValue, out int result))
            {
                return result;
            }

            return 0;
        }

        /// <summary>
        /// 验证源数据
        /// </summary>
        private ValidationResult ValidateSourceData(ProviderExcelData sourceData)
        {
            var result = new ValidationResult { IsValid = true };

            // 检查必要字段
            if (string.IsNullOrEmpty(sourceData.受给者证番号))
            {
                result.IsValid = false;
                result.ErrorMessage = "受给者证番号不能为空";
                return result;
            }

            if (string.IsNullOrEmpty(sourceData.支给决定障害者氏名))
            {
                result.IsValid = false;
                result.ErrorMessage = "支给决定障害者氏名不能为空";
                return result;
            }

            if (string.IsNullOrEmpty(sourceData.事业所番号))
            {
                result.IsValid = false;
                result.ErrorMessage = "事业所番号不能为空";
                return result;
            }

            if (sourceData.服务明细 == null || sourceData.服务明细.Count == 0)
            {
                result.IsValid = false;
                result.ErrorMessage = "服务明细不能为空";
                return result;
            }

            // 检查服务明细的完整性
            foreach (var service in sourceData.服务明细)
            {
                if (string.IsNullOrEmpty(service.服务代码))
                {
                    result.Warnings.Add($"发现空的服务代码: {service.服务内容}");
                }

                if (string.IsNullOrEmpty(service.服务内容))
                {
                    result.Warnings.Add($"发现空的服务内容: {service.服务代码}");
                }
            }

            // 检查日期格式
            if (!string.IsNullOrEmpty(sourceData.开始年月日) && !IsValidDate(sourceData.开始年月日))
            {
                result.Warnings.Add($"开始年月日格式可能不正确: {sourceData.开始年月日}");
            }

            if (!string.IsNullOrEmpty(sourceData.终了年月日) && !IsValidDate(sourceData.终了年月日))
            {
                result.Warnings.Add($"终了年月日格式可能不正确: {sourceData.终了年月日}");
            }

            return result;
        }

        /// <summary>
        /// 检查日期格式是否有效
        /// </summary>
        private bool IsValidDate(string dateString)
        {
            return DateTime.TryParse(dateString, out _) ||
                   dateString.Contains("-") && dateString.Split('-').Length == 3;
        }


        /// <summary>
        /// 将OCR识别的JSON转换为导入数据格式
        /// </summary>
        public List<RecipientImportData> ConvertJsonToImportData(JObject json)
        {
            var results = new List<RecipientImportData>();

            try
            {
                // 提取基本信息
                var title = json["title"]?.ToString() ?? "";
                var date = json["date"];
                var recipientInfo = json["recipient_info"];
                var businessOfficeInfo = json["business_office_info"];
                var basicCompensation = json["basic_compensation"] as JArray;
                var additionItems = json["addition_items"] as JArray;
                var psychiatricAddition = json["psychiatric_medical_cooperation_addition"];

                // 构建年月信息
                var era = date?["era"]?.ToString() ?? "";
                var year = date?["year"]?.ToString() ?? "";
                var month = date?["month"]?.ToString() ?? "";
                var yearMonth = $"{era}{year}年{month}月";

                // 构建服务提供年月（YYYYMM格式）
                var serviceYearMonth = "";
                if (int.TryParse(year, out int y) && int.TryParse(month, out int m))
                {
                    var actualYear = 2018 + y; // 令和元年是2019年，令和7年是2025年
                    serviceYearMonth = $"{actualYear:D4}{m:D2}";
                }

                // 处理基本报酬项目
                if (basicCompensation != null)
                {
                    foreach (var item in basicCompensation)
                    {
                        var importData = CreateRecipientImportData(
                            yearMonth, serviceYearMonth, recipientInfo, businessOfficeInfo, item);
                        if (importData != null)
                        {
                            results.Add(importData);
                        }

                    }
                }

                // 处理加算项目
                if (additionItems != null)
                {
                    foreach (var item in additionItems)
                    {
                        // 只处理有金额的加算项目
                        var monthlyAmount = item["monthly_calculation_amount"]?.ToString();
                        var monthlyPrice = item["calculation_unit_price"]?.ToString();
                        if (!string.IsNullOrEmpty(monthlyAmount) || !string.IsNullOrEmpty(monthlyPrice))
                        {
                            var importData = CreateRecipientImportData(
                                yearMonth, serviceYearMonth, recipientInfo, businessOfficeInfo, item);
                            if (importData != null)
                            {
                                results.Add(importData);
                            }
                            //results.Add(importData);
                        }
                    }
                }

                // 处理精神科医疗连携体制加算
                /* if (psychiatricAddition != null)
                 {
                     var amount = psychiatricAddition["amount"]?.ToString();
                     if (!string.IsNullOrEmpty(amount))
                     {
                         var psychiatricItem = new JObject
                         {
                             ["service_code"] = "精神科医療連携",
                             ["service_content"] = "精神科医療連携体制加算",
                             ["calculation_unit_price"] = psychiatricAddition["unit_price"]?.ToString(),
                             ["usage_days"] = psychiatricAddition["usage_days"]?.ToString(),
                             ["monthly_calculation_amount"] = amount,
                             ["remarks"] = "精神科医療連携体制加算"
                         };

                         var importData = CreateRecipientImportData(
                             yearMonth, serviceYearMonth, recipientInfo, businessOfficeInfo, psychiatricItem);
                         results.Add(importData);
                     }
                 }*/
            }
            catch (Exception ex)
            {
                throw new Exception($"JSON数据转换失败: {ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// 创建受给者导入数据
        /// </summary>
        private RecipientImportData CreateRecipientImportData(
            string yearMonth, string serviceYearMonth,
            JToken recipientInfo, JToken businessOfficeInfo, JToken serviceItem)
        {
            var excelService = new ExcelDataService(_databasePath);
            // 获取基准数据
            var businessData = excelService.GetSheetData("事业者数据");
            var serviceCodeData = excelService.GetSheetData("服务代码数据");

            var matchedBusiness = businessData.FirstOrDefault(b =>
            {
                var hasIdMatch = b.ContainsKey("事業者番号") &&
                                 b["事業者番号"] != null &&
                                 b["事業者番号"].ToString() == (businessOfficeInfo?["office_number"]?.ToString() ?? "");

                var hasNameMatch = b.ContainsKey("事業者名称") &&
                                   b["事業者名称"] != null &&
                                   b["事業者名称"].ToString().Contains(businessOfficeInfo?["business_name"]?.ToString() ?? "");

                return hasIdMatch || hasNameMatch;
            });
            // 匹配服务代码数据 - 支持多种匹配方式
            var matchedService = serviceCodeData.FirstOrDefault(s =>
            {
                var hasCodeMatch = s.ContainsKey("code") &&
                                   s["code"] != null &&
                                   s["code"].ToString() == (serviceItem?["service_code"]?.ToString() ?? "");

                var hasNameMatch = s.ContainsKey("サービス内容略称") &&
                                   s["サービス内容略称"] != null &&
                                   s["サービス内容略称"].ToString().Contains(serviceItem?["service_content"]?.ToString() ?? serviceItem?["content"]?.ToString() ?? "");
                return hasCodeMatch || hasNameMatch;
            });


            if (matchedBusiness == null || matchedService == null)
            {
                return null;
            }

            return new RecipientImportData
            {
                No = 0, // 将在导入时设置
                登録日 = yearMonth,
                事業者番号 = businessOfficeInfo?["office_number"]?.ToString() ?? "",
                事業者郵便番号 = GetBusinessProperty(matchedBusiness, "郵便番号"),
                事業者住所 = GetBusinessProperty(matchedBusiness, "所在地"),
                事業者名称 = businessOfficeInfo?["business_name"]?.ToString() ?? "",
                代表者名 = GetBusinessProperty(matchedBusiness, "代表者名"),
                代表者役職 = GetBusinessProperty(matchedBusiness, "代表者役職"),
                サービス提供年月 = serviceYearMonth,
                明細書件数 = 1,
                請求金額 = ParseDecimalFromString(serviceItem?["monthly_calculation_amount"]?.ToString()),
                第三者評価 = yearMonth, // OCR中没有此信息
                受給者番号 = recipientInfo?["certificate_number"]?.ToString() ?? "",
                支給決定障害者氏名 = recipientInfo?["support_decision_disabled_person_name"]?.ToString() ?? "",
                支給決定に係る障害児氏名 = recipientInfo?["support_decision_disabled_child_name"]?.ToString() ?? "",
                障害支援区分 = recipientInfo?["disability_support_classification"]?.ToString() ?? "",
                事業者名称2 = businessOfficeInfo?["business_name"]?.ToString() ?? "",
                地域区分 = businessOfficeInfo?["regional_classification"]?.ToString() ?? "",
                旧身体療護施設区分 = businessOfficeInfo?["former_physical_care_facility_classification"]?.ToString() ?? "",
                精神科医療連携体制加算 = businessOfficeInfo?["psychiatric_medical_cooperation_system_addition"]?.ToString() ?? "",
                開始年月日 = FormatDateFromString(recipientInfo?["start_date"]?.ToString()),
                終了年月日 = FormatDateFromString(recipientInfo?["end_date"]?.ToString()),
                利用日数全体 = ParseIntFromString(recipientInfo?["usage_days"]?.ToString()),
                サービスコード = serviceItem?["service_code"]?.ToString() ?? "",
                サービス内容 = serviceItem?["service_content"]?.ToString() ?? serviceItem?["content"]?.ToString() ?? "",
                算定単価額 = ParseDecimalFromString(serviceItem?["calculation_unit_price"]?.ToString()),
                利用日数 = ParseIntFromString(serviceItem?["usage_days"]?.ToString()),
                当月算定額 = ParseDecimalFromString(serviceItem?["monthly_calculation_amount"]?.ToString()),
                摘要 = serviceItem?["remarks"]?.ToString() ?? "",
                Status = "处理中"
            };
        }

        /// <summary>
        /// 从字符串解析decimal值
        /// </summary>
        private decimal ParseDecimalFromString(string value)
        {
            if (string.IsNullOrEmpty(value)) return 0;

            // 移除逗号和其他非数字字符（保留小数点）
            var cleanValue = System.Text.RegularExpressions.Regex.Replace(value, @"[^\d.]", "");

            if (decimal.TryParse(cleanValue, out decimal result))
                return result;

            return 0;
        }

        /// <summary>
        /// 从字符串解析int值
        /// </summary>
        private int ParseIntFromString(string value)
        {
            if (string.IsNullOrEmpty(value)) return 0;

            // 移除非数字字符
            var cleanValue = System.Text.RegularExpressions.Regex.Replace(value, @"[^\d]", "");

            if (int.TryParse(cleanValue, out int result))
                return result;

            return 0;
        }

        /// <summary>
        /// 格式化日期字符串
        /// </summary>
        private string FormatDateFromString(string dateStr)
        {
            if (string.IsNullOrEmpty(dateStr)) return "";

            try
            {
                // 尝试解析日期格式 "2025/2/1"
                if (DateTime.TryParse(dateStr, out DateTime date))
                {
                    return date.ToString("yyyy/MM/dd");
                }

                return dateStr; // 如果解析失败，返回原字符串
            }
            catch
            {
                return dateStr;
            }
        }
    }

    /// <summary>
    /// 导入结果
    /// </summary>
    public class ImportResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        public int ImportedRecordCount { get; set; }
        public ImportSummary Details { get; set; }
    }

    /// <summary>
    /// 导入汇总信息
    /// </summary>
    public class ImportSummary
    {
        public string FileName { get; set; }
        public string 年月 { get; set; }
        public string 受给者姓名 { get; set; }
        public string 事业所名称 { get; set; }
        public int 原始服务明细数 { get; set; }
        public int 匹配成功的事业者数 { get; set; }
        public int 匹配成功的服务代码数 { get; set; }
        public int 最终导入记录数 { get; set; }
        public List<string> 匹配失败的服务代码 { get; set; } = new List<string>();
        public List<string> 处理警告 { get; set; } = new List<string>();
    }

    /// <summary>
    /// 数据验证结果
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// 事业者Excel数据
    /// </summary>
    public class ProviderExcelData
    {
        public string 年月 { get; set; }
        public string 受给者证番号 { get; set; }
        public string 支给决定障害者氏名 { get; set; }
        public string 支给决定に係る障害児氏名 { get; set; }
        public string 事业所番号 { get; set; }
        public string 事业者及びその事业所の名称 { get; set; }
        public string 障害支援区分 { get; set; }
        public string 开始年月日 { get; set; }
        public string 终了年月日 { get; set; }
        public string 利用日数 { get; set; }

        public string 旧身体療護施設区分 { get; set; }

        public string 精神科医療連携体制加算 { get; set; }

        public string 地域区分 { get; set; }

        public string 摘要 { get; set; }

        public List<ServiceDetail> 服务明细 { get; set; } = new List<ServiceDetail>();
    }

    /// <summary>
    /// 服务明细
    /// </summary>
    public class ServiceDetail
    {
        public string 服务代码 { get; set; }
        public string 服务内容 { get; set; }
        public string 单定价格 { get; set; }
        public string 利用日数 { get; set; }
        public string 当月费定额 { get; set; }

        public string 摘要 { get; set; }
    }

    /// <summary>
    /// 受给者导入数据（根据新的字段结构）
    /// </summary>
    public class RecipientImportData
    {
        public int No { get; set; }
        public string 登録日 { get; set; }
        public string 事業者番号 { get; set; }
        public string 事業者郵便番号 { get; set; }
        public string 事業者住所 { get; set; }
        public string 事業者名称 { get; set; }
        public string 代表者名 { get; set; }
        public string 代表者役職 { get; set; }
        public string サービス提供年月 { get; set; }
        public int 明細書件数 { get; set; }
        public decimal 請求金額 { get; set; }
        public string 第三者評価 { get; set; }
        public string 受給者番号 { get; set; }
        public string 支給決定障害者氏名 { get; set; }
        public string 支給決定に係る障害児氏名 { get; set; }
        public string 障害支援区分 { get; set; }
        public string 事業者名称2 { get; set; }
        public string 地域区分 { get; set; }
        public string 旧身体療護施設区分 { get; set; }
        public string 精神科医療連携体制加算 { get; set; }
        public string 開始年月日 { get; set; }
        public string 終了年月日 { get; set; }
        public int 利用日数全体 { get; set; }
        public string サービスコード { get; set; }
        public string サービス内容 { get; set; }
        public decimal 算定単価額 { get; set; }
        public int 利用日数 { get; set; }
        public decimal 当月算定額 { get; set; }
        public string 摘要 { get; set; }
        public string Status { get; set; }
    }
}